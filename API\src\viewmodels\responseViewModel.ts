/**
 * Generic Response ViewModels for consistent API responses
 */

export interface SuccessResponseViewModel<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  timestamp: string;
}

export interface ErrorResponseViewModel {
  success: boolean;
  error: string;
  message?: string;
  details?: any;
  timestamp: string;
  path: string;
  method: string;
  stack?: string;
}

export interface PaginationViewModel {
  page: number;
  limit: number;
  total: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponseViewModel<T = any> {
  success: boolean;
  data: T[];
  pagination: PaginationViewModel;
  timestamp: string;
}

/**
 * Create a success response with data
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string
): SuccessResponseViewModel<T> {
  const response: SuccessResponseViewModel<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
  };

  if (message) {
    response.message = message;
  }

  return response;
}

/**
 * Create a success response without data
 */
export function createSuccessMessageResponse(
  message: string
): SuccessResponseViewModel {
  return {
    success: true,
    message,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create an error response
 */
export function createErrorResponse(
  error: string,
  path: string,
  method: string,
  details?: any,
  stack?: string
): ErrorResponseViewModel {
  const response: ErrorResponseViewModel = {
    success: false,
    error,
    timestamp: new Date().toISOString(),
    path,
    method,
  };

  if (details) {
    response.details = details;
  }

  if (stack) {
    response.stack = stack;
  }

  return response;
}

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): PaginatedResponseViewModel<T> {
  const pages = Math.ceil(total / limit);

  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Validate pagination parameters
 */
export function validatePaginationParams(
  page?: string | number,
  limit?: string | number
): { page: number; limit: number } {
  const validatedPage = Math.max(1, parseInt(String(page || 1), 10) || 1);
  const validatedLimit = Math.min(
    100,
    Math.max(1, parseInt(String(limit || 20), 10) || 20)
  );

  return {
    page: validatedPage,
    limit: validatedLimit,
  };
}

/**
 * Calculate pagination skip value
 */
export function calculateSkip(page: number, limit: number): number {
  return (page - 1) * limit;
}
