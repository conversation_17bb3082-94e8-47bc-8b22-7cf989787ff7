import { socketService } from '../services/socketService';
import { notificationService } from '../services/notificationService';
import { getSocketIO } from '../config/socket';
import { SOCKET_EVENTS, SOCKET_ROOMS } from '../config/socket';
import {
  EventUpdateType,
  JobUpdateType,
  PostUpdateType,
  SocketNotification,
  NotificationType,
} from '../types/socket';
import { Logger } from '../services/loggerService';

// Utility functions for triggering real-time updates from controllers

export class SocketUtils {
  // Trigger follow request notification
  static async triggerFollowRequest(
    followerId: number,
    followingId: number,
    tenantId: number,
  ) {
    try {
      await notificationService.sendFollowRequestNotification(
        followerId,
        followingId,
        tenantId,
      );
    } catch (error) {
      Logger.error('Error triggering follow request:', error);
    }
  }

  // Trigger follow accepted notification
  static async triggerFollowAccepted(
    followerId: number,
    followingId: number,
    tenantId: number,
  ) {
    try {
      await notificationService.sendFollowAcceptedNotification(
        followerId,
        followingId,
        tenantId,
      );
    } catch (error) {
      Logger.error('Error triggering follow accepted:', error);
    }
  }

  // Trigger connection request notification
  static async triggerConnectionRequest(
    requesterId: number,
    targetId: number,
    tenantId: number,
  ) {
    try {
      await notificationService.sendConnectionRequestNotification(
        requesterId,
        targetId,
        tenantId,
      );
    } catch (error) {
      Logger.error('Error triggering connection request:', error);
    }
  }

  // Trigger connection accepted notification
  static async triggerConnectionAccepted(
    requesterId: number,
    accepterId: number,
    tenantId: number,
  ) {
    try {
      await notificationService.sendConnectionAcceptedNotification(
        requesterId,
        accepterId,
        tenantId,
      );
    } catch (error) {
      Logger.error('Error triggering connection accepted:', error);
    }
  }

  // Trigger message notification
  static async triggerMessageNotification(
    senderId: number,
    receiverId: number,
    tenantId: number,
    content: string,
  ) {
    try {
      await notificationService.sendMessageNotification(
        senderId,
        receiverId,
        tenantId,
        content,
      );
    } catch (error) {
      Logger.error('Error triggering message notification:', error);
    }
  }

  // Trigger event notifications
  static async triggerEventCreated(
    eventId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendEventNotification(
        eventId,
        tenantId,
        EventUpdateType.CREATED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering event created:', error);
    }
  }

  static async triggerEventUpdated(
    eventId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendEventNotification(
        eventId,
        tenantId,
        EventUpdateType.UPDATED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering event updated:', error);
    }
  }

  static async triggerEventDeleted(
    eventId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendEventNotification(
        eventId,
        tenantId,
        EventUpdateType.DELETED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering event deleted:', error);
    }
  }

  // Trigger job notifications
  static async triggerJobPosted(
    jobId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendJobNotification(
        jobId,
        tenantId,
        JobUpdateType.POSTED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering job posted:', error);
    }
  }

  static async triggerJobUpdated(
    jobId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendJobNotification(
        jobId,
        tenantId,
        JobUpdateType.UPDATED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering job updated:', error);
    }
  }

  static async triggerJobDeleted(
    jobId: number,
    tenantId: number,
    authorId: number,
  ) {
    try {
      await notificationService.sendJobNotification(
        jobId,
        tenantId,
        JobUpdateType.DELETED,
        authorId,
      );
    } catch (error) {
      Logger.error('Error triggering job deleted:', error);
    }
  }

  // Trigger post notifications
  static async triggerPostLiked(
    postId: number,
    tenantId: number,
    likerId: number,
    postAuthorId: number,
  ) {
    try {
      await notificationService.sendPostNotification(
        postId,
        tenantId,
        PostUpdateType.LIKED,
        likerId,
        postAuthorId,
      );
    } catch (error) {
      Logger.error('Error triggering post liked:', error);
    }
  }

  static async triggerPostCommented(
    postId: number,
    tenantId: number,
    commenterId: number,
    postAuthorId: number,
  ) {
    try {
      await notificationService.sendPostNotification(
        postId,
        tenantId,
        PostUpdateType.COMMENTED,
        commenterId,
        postAuthorId,
      );
    } catch (error) {
      Logger.error('Error triggering post commented:', error);
    }
  }

  // Trigger system notification
  static async triggerSystemNotification(
    tenantId: number,
    title: string,
    message: string,
    data?: any,
  ) {
    try {
      await notificationService.sendSystemNotification(
        tenantId,
        title,
        message,
        data,
      );
    } catch (error) {
      Logger.error('Error triggering system notification:', error);
    }
  }

  // Get online users for a tenant
  static getOnlineUsers(tenantId: number) {
    try {
      return socketService.getOnlineUsersForTenant(tenantId);
    } catch (error) {
      Logger.error('Error getting online users:', error);
      return [];
    }
  }

  // Check if user is online
  static isUserOnline(userId: number): boolean {
    try {
      return socketService.isUserOnline(userId);
    } catch (error) {
      Logger.error('Error checking if user is online:', error);
      return false;
    }
  }

  // Send custom notification to specific user
  static async sendCustomNotification(
    userId: number,
    tenantId: number,
    type: NotificationType,
    title: string,
    message: string,
    data?: any,
  ) {
    try {
      const notification: SocketNotification = {
        userId,
        tenantId,
        type,
        title,
        message,
        data,
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(userId, notification);
    } catch (error) {
      Logger.error('Error sending custom notification:', error);
    }
  }

  // Send custom notification to multiple users
  static async sendCustomNotificationToUsers(
    userIds: number[],
    tenantId: number,
    type: NotificationType,
    title: string,
    message: string,
    data?: any,
  ) {
    try {
      const notification: SocketNotification = {
        userId: 0, // Will be set for each user
        tenantId,
        type,
        title,
        message,
        data,
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUsers(userIds, notification);
    } catch (error) {
      Logger.error('Error sending custom notification to users:', error);
    }
  }

  // Broadcast custom event to tenant
  static async broadcastToTenant(tenantId: number, event: string, data: any) {
    try {
      const io = getSocketIO();
      io.to(SOCKET_ROOMS.TENANT(tenantId)).emit(event, data);
      Logger.info(`Broadcasted ${event} to tenant ${tenantId}`);
    } catch (error) {
      Logger.error('Error broadcasting to tenant:', error);
    }
  }

  // Broadcast custom event to specific users
  static async broadcastToUsers(userIds: number[], event: string, data: any) {
    try {
      const io = getSocketIO();
      userIds.forEach(userId => {
        io.to(SOCKET_ROOMS.USER(userId)).emit(event, data);
      });
      Logger.info(`Broadcasted ${event} to ${userIds.length} users`);
    } catch (error) {
      Logger.error('Error broadcasting to users:', error);
    }
  }

  // Send real-time activity update
  static async sendActivityUpdate(
    tenantId: number,
    activityType: string,
    userId: number,
    data?: any,
  ) {
    try {
      const activityData = {
        type: activityType,
        userId,
        timestamp: new Date(),
        data,
      };

      await this.broadcastToTenant(tenantId, 'activity_update', activityData);
    } catch (error) {
      Logger.error('Error sending activity update:', error);
    }
  }

  // Get room members
  static async getRoomMembers(roomId: string): Promise<number[]> {
    try {
      return await socketService.getRoomMembers(roomId);
    } catch (error) {
      Logger.error('Error getting room members:', error);
      return [];
    }
  }

  // Force disconnect user (admin function)
  static forceDisconnectUser(userId: number, reason?: string): void {
    try {
      const io = getSocketIO();
      const socketIds = socketService.getUserSocketIds(userId);

      socketIds.forEach(socketId => {
        const socket = io.sockets.sockets.get(socketId);
        if (socket) {
          socket.emit(SOCKET_EVENTS.ERROR, {
            code: 'FORCE_DISCONNECT',
            message: reason ?? 'You have been disconnected by an administrator',
          });
          socket.disconnect(true);
        }
      });

      Logger.info(
        `Force disconnected user ${userId}, reason: ${reason ?? 'Admin action'}`,
      );
    } catch (error) {
      Logger.error('Error force disconnecting user:', error);
    }
  }

  // Get server statistics
  static getServerStats(): Record<string, unknown> {
    try {
      const io = getSocketIO();
      const onlineUsers = socketService.getOnlineUsersForTenant(0); // Get all online users

      return {
        totalConnections: io.engine.clientsCount,
        totalOnlineUsers: onlineUsers.length,
        serverUptime: process.uptime(),
        timestamp: new Date(),
      };
    } catch (error) {
      Logger.error('Error getting server stats:', error);
      return {
        totalConnections: 0,
        totalOnlineUsers: 0,
        serverUptime: 0,
        timestamp: new Date(),
      };
    }
  }
}

export default SocketUtils;
