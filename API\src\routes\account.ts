import express from 'express';
import { body, param } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation';
import * as AccountController from '../controllers/accountController';

const router = express.Router();

/**
 * @swagger
 * /api/account/register:
 *   post:
 *     summary: Register a new user account
 *     tags: [Account]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - full_name
 *               - role
 *               - tenant_id
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               full_name:
 *                 type: string
 *                 minLength: 2
 *               mobile_number:
 *                 type: string
 *               usn:
 *                 type: string
 *               course_name:
 *                 type: string
 *               batch_year:
 *                 type: integer
 *               role:
 *                 type: string
 *                 enum: [STUDENT, ALUMNUS, TENANT_ADMIN]
 *               tenant_id:
 *                 type: integer
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: User already exists
 */
router.post(
  '/register',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }),
    body('full_name').isLength({ min: 2 }).trim(),
    body('role').isIn(['STUDENT', 'ALUMNUS', 'TENANT_ADMIN']),
    body('tenant_id').isInt({ min: 1 }),
    body('mobile_number').optional().isMobilePhone('any'),
    body('usn').optional().isLength({ min: 1 }),
    body('course_name').optional().isLength({ min: 1 }),
    body('batch_year')
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() + 10 }),
  ],
  handleValidationErrors,
  AccountController.register
);

/**
 * @swagger
 * /api/account/verify-email/{token}:
 *   post:
 *     summary: Verify email address
 *     tags: [Account]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tenant_id
 *             properties:
 *               tenant_id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post(
  '/verify-email/:token',
  [param('token').isLength({ min: 1 }), body('tenant_id').isInt({ min: 1 })],
  handleValidationErrors,
  AccountController.verifyEmail
);

/**
 * @swagger
 * /api/account/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Account]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - tenant_id
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               tenant_id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Password reset email sent
 *       400:
 *         description: Validation error
 */
router.post(
  '/forgot-password',
  [
    body('email').isEmail().normalizeEmail(),
    body('tenant_id').isInt({ min: 1 }),
  ],
  handleValidationErrors,
  AccountController.forgotPassword
);

/**
 * @swagger
 * /api/account/reset-password/{token}:
 *   post:
 *     summary: Reset password using reset token
 *     tags: [Account]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *               - tenant_id
 *             properties:
 *               password:
 *                 type: string
 *                 minLength: 8
 *               tenant_id:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Password reset successfully
 *       400:
 *         description: Invalid or expired token
 */
router.post(
  '/reset-password/:token',
  [
    param('token').isLength({ min: 1 }),
    body('password').isLength({ min: 8 }),
    body('tenant_id').isInt({ min: 1 }),
  ],
  handleValidationErrors,
  AccountController.resetPassword
);

export default router;
