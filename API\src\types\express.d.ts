import { UserRole, UserStatus } from '@prisma/client';
import { Request } from 'express';

declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        email: string;
        role: UserRole;
        status: UserStatus;
        id: string;
        tenant_id: number;
      };
    }
  }
}

export interface AuthenticatedRequest<P = any> extends Request<P> {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
    tenant_id: number;
  };
}

export {};
