/// <reference types="node" />
import { PrismaClient, UserRole, UserStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create multiple tenants with realistic examples
  const tenants = [
    {
      name: 'Indian Institute of Technology Bombay',
      subdomain: 'iitb',
      logo_url: 'https://www.iitb.ac.in/sites/default/files/iitb-logo.png',
      is_active: true,
    },
    {
      name: 'National Institute of Technology Karnataka',
      subdomain: 'nitk',
      logo_url: 'https://nitk.ac.in/images/nitk-logo.png',
      is_active: true,
    },
    {
      name: 'Indian Institute of Management Bangalore',
      subdomain: 'iimb',
      logo_url: 'https://www.iimb.ac.in/sites/default/files/iimb-logo.png',
      is_active: true,
    },
    {
      name: 'Delhi University',
      subdomain: 'du',
      logo_url: 'https://www.du.ac.in/images/du-logo.png',
      is_active: true,
    },
    {
      name: 'Manipal Institute of Technology',
      subdomain: 'mit',
      logo_url:
        'https://manipal.edu/content/dam/manipal/mu/mit/images/mit-logo.png',
      is_active: true,
    },
    {
      name: 'Vellore Institute of Technology',
      subdomain: 'vit',
      logo_url: 'https://vit.ac.in/images/vit-logo.png',
      is_active: true,
    },
  ];

  console.log('🏫 Creating tenants...');
  const createdTenants: any[] = [];

  for (const tenantData of tenants) {
    const tenant = await prisma.tenant.upsert({
      where: { subdomain: tenantData.subdomain },
      update: tenantData,
      create: tenantData,
    });
    createdTenants.push(tenant);
    console.log(`✅ Created/Updated: ${tenant.name} (${tenant.subdomain})`);
  }

  // Create courses for each tenant
  console.log('📚 Creating courses...');

  const coursesByTenant: Record<string, string[]> = {
    iitb: [
      'Computer Science and Engineering',
      'Electrical Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Chemical Engineering',
      'Aerospace Engineering',
      'Metallurgical Engineering and Materials Science',
    ],
    nitk: [
      'Computer Science and Engineering',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Information Technology',
      'Electrical Engineering',
      'Chemical Engineering',
    ],
    iimb: [
      'Master of Business Administration',
      'Executive MBA',
      'Post Graduate Programme in Management',
      'Fellow Programme in Management',
      'Certificate Programme in General Management',
    ],
    du: [
      'Bachelor of Arts',
      'Bachelor of Science',
      'Bachelor of Commerce',
      'Master of Arts',
      'Master of Science',
      'Master of Commerce',
      'Bachelor of Technology',
    ],
    mit: [
      'Computer Science and Engineering',
      'Information Technology',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Biotechnology',
    ],
    vit: [
      'Computer Science and Engineering',
      'Electronics and Communication Engineering',
      'Mechanical Engineering',
      'Civil Engineering',
      'Information Technology',
      'Electrical and Electronics Engineering',
      'Chemical Engineering',
    ],
  };

  for (const tenant of createdTenants) {
    const courses = coursesByTenant[tenant.subdomain] || [];

    for (const courseName of courses) {
      // Check if course already exists
      const existingCourse = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: courseName,
        },
      });

      if (!existingCourse) {
        await prisma.course.create({
          data: {
            tenant_id: tenant.id,
            course_name: courseName,
          },
        });
      }
    }

    console.log(`📖 Added ${courses.length} courses for ${tenant.name}`);
  }

  // Create admin users for each tenant
  console.log('👤 Creating admin users...');
  const adminPassword = await bcrypt.hash('AdminPass123!', 12);

  const adminEmails: Record<string, string> = {
    iitb: '<EMAIL>',
    nitk: '<EMAIL>',
    iimb: '<EMAIL>',
    du: '<EMAIL>',
    mit: '<EMAIL>',
    vit: '<EMAIL>',
  };

  for (const tenant of createdTenants) {
    const adminEmail = adminEmails[tenant.subdomain];
    if (adminEmail) {
      await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: adminEmail,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: adminEmail,
          password_hash: adminPassword,
          full_name: `${tenant.name} Administrator`,
          usn: `ADMIN${tenant.id.toString().padStart(3, '0')}`,
          role: UserRole.TENANT_ADMIN,
          account_status: UserStatus.APPROVED,
        },
      });
      console.log(`👨‍💼 Created admin for ${tenant.name}: ${adminEmail}`);
    }
  }

  // Create sample alumni users for each tenant
  console.log('🎓 Creating sample alumni users...');

  const sampleAlumniByTenant: Record<string, any[]> = {
    iitb: [
      // Senior Alumni - Tech Leaders
      {
        email: '<EMAIL>',
        full_name: 'Rajesh Kumar',
        usn: 'CS2018001',
        batch_year: 2018,
        course_name: 'Computer Science and Engineering',
        company: 'Google',
        job_title: 'Software Engineer',
        location: 'Bangalore, India',
        linkedin_url: 'https://linkedin.com/in/rajesh-kumar-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Priya Sharma',
        usn: 'EE2017002',
        batch_year: 2017,
        course_name: 'Electrical Engineering',
        company: 'Microsoft',
        job_title: 'Senior Software Engineer',
        location: 'Hyderabad, India',
        linkedin_url: 'https://linkedin.com/in/priya-sharma-iitb',
      },
      // Mid-level Professionals
      {
        email: '<EMAIL>',
        full_name: 'Arjun Mehta',
        usn: 'ME2019003',
        batch_year: 2019,
        course_name: 'Mechanical Engineering',
        company: 'Tesla',
        job_title: 'Mechanical Design Engineer',
        location: 'San Francisco, USA',
        linkedin_url: 'https://linkedin.com/in/arjun-mehta-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Sneha Patel',
        usn: 'CH2020004',
        batch_year: 2020,
        course_name: 'Chemical Engineering',
        company: 'Reliance Industries',
        job_title: 'Process Engineer',
        location: 'Mumbai, India',
        linkedin_url: 'https://linkedin.com/in/sneha-patel-iitb',
      },
      // Recent Graduates
      {
        email: '<EMAIL>',
        full_name: 'Vikram Singh',
        usn: 'CS2021005',
        batch_year: 2021,
        course_name: 'Computer Science and Engineering',
        company: 'Amazon',
        job_title: 'Software Development Engineer I',
        location: 'Bangalore, India',
        linkedin_url: 'https://linkedin.com/in/vikram-singh-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Anita Reddy',
        usn: 'AE2021006',
        batch_year: 2021,
        course_name: 'Aerospace Engineering',
        company: 'ISRO',
        job_title: 'Junior Scientist',
        location: 'Bangalore, India',
        linkedin_url: 'https://linkedin.com/in/anita-reddy-iitb',
      },
      // Entrepreneurs
      {
        email: '<EMAIL>',
        full_name: 'Rohit Agarwal',
        usn: 'CS2016007',
        batch_year: 2016,
        course_name: 'Computer Science and Engineering',
        company: 'TechStart Solutions (Founder)',
        job_title: 'CEO & Founder',
        location: 'Pune, India',
        linkedin_url: 'https://linkedin.com/in/rohit-agarwal-iitb',
      },
      // International Alumni
      {
        email: '<EMAIL>',
        full_name: 'Meera Joshi',
        usn: 'EE2015008',
        batch_year: 2015,
        course_name: 'Electrical Engineering',
        company: 'Apple',
        job_title: 'Hardware Engineer',
        location: 'Cupertino, USA',
        linkedin_url: 'https://linkedin.com/in/meera-joshi-iitb',
      },
      // Academia
      {
        email: '<EMAIL>',
        full_name: 'Dr. Suresh Nair',
        usn: 'MT2014009',
        batch_year: 2014,
        course_name: 'Metallurgical Engineering and Materials Science',
        company: 'Stanford University',
        job_title: 'Assistant Professor',
        location: 'Stanford, USA',
        linkedin_url: 'https://linkedin.com/in/dr-suresh-nair-iitb',
      },
      // Diverse Industries
      {
        email: '<EMAIL>',
        full_name: 'Kavita Sharma',
        usn: 'CE2019010',
        batch_year: 2019,
        course_name: 'Civil Engineering',
        company: 'L&T Construction',
        job_title: 'Project Manager',
        location: 'Delhi, India',
        linkedin_url: 'https://linkedin.com/in/kavita-sharma-iitb',
      },
    ],
    nitk: [
      {
        email: '<EMAIL>',
        full_name: 'Amit Patel',
        usn: 'CS2019001',
        batch_year: 2019,
        course_name: 'Computer Science and Engineering',
        company: 'Amazon',
        job_title: 'Software Development Engineer',
        location: 'Bangalore, India',
      },
    ],
    iimb: [
      {
        email: '<EMAIL>',
        full_name: 'Neha Gupta',
        usn: '**********',
        batch_year: 2020,
        course_name: 'Master of Business Administration',
        company: 'McKinsey & Company',
        job_title: 'Business Analyst',
        location: 'Mumbai, India',
      },
    ],
  };

  for (const tenant of createdTenants) {
    const alumniUsers = sampleAlumniByTenant[tenant.subdomain] || [];

    for (const userData of alumniUsers) {
      const password = await bcrypt.hash('AlumniPass123!', 12);

      // Find the course for this user
      const course = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: userData.course_name,
        },
      });

      const user = await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: userData.email,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: userData.email,
          password_hash: password,
          full_name: userData.full_name,
          usn: userData.usn,
          role: UserRole.ALUMNUS,
          account_status: UserStatus.APPROVED,
        },
      });

      // Create user profile
      await prisma.userProfile.upsert({
        where: {
          user_id: user.id,
        },
        update: {},
        create: {
          user_id: user.id,
          tenant_id: tenant.id,
          course_id: course?.id || null,
          batch_year: userData.batch_year,
          current_location: userData.location,
          company: userData.company,
          job_title: userData.job_title,
          linkedin_url: userData.linkedin_url || null,
          privacy_settings: {
            show_email: false,
            show_mobile: false,
            show_linkedin: true,
            show_location: true,
            show_company: true,
            show_job_title: true,
          },
        },
      });

      console.log(`🎓 Created alumni: ${user.email} (${tenant.name})`);
    }
  }

  // Create sample student users for each tenant
  console.log('👨‍🎓 Creating sample student users...');

  const sampleStudentsByTenant: Record<string, any[]> = {
    iitb: [
      // Final Year Students
      {
        email: '<EMAIL>',
        full_name: 'Arjun Reddy',
        usn: 'CS2021001',
        batch_year: 2025,
        course_name: 'Computer Science and Engineering',
        linkedin_url: 'https://linkedin.com/in/arjun-reddy-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Kavya Nair',
        usn: 'EE2021002',
        batch_year: 2025,
        course_name: 'Electrical Engineering',
        linkedin_url: 'https://linkedin.com/in/kavya-nair-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Rahul Gupta',
        usn: 'ME2021003',
        batch_year: 2025,
        course_name: 'Mechanical Engineering',
        linkedin_url: 'https://linkedin.com/in/rahul-gupta-iitb',
      },
      // Third Year Students
      {
        email: '<EMAIL>',
        full_name: 'Pooja Singh',
        usn: 'CS2022004',
        batch_year: 2026,
        course_name: 'Computer Science and Engineering',
        linkedin_url: 'https://linkedin.com/in/pooja-singh-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Amit Kumar',
        usn: 'CH2022005',
        batch_year: 2026,
        course_name: 'Chemical Engineering',
        linkedin_url: 'https://linkedin.com/in/amit-kumar-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Riya Patel',
        usn: 'AE2022006',
        batch_year: 2026,
        course_name: 'Aerospace Engineering',
        linkedin_url: 'https://linkedin.com/in/riya-patel-iitb',
      },
      // Second Year Students
      {
        email: '<EMAIL>',
        full_name: 'Karan Shah',
        usn: 'EE2023007',
        batch_year: 2027,
        course_name: 'Electrical Engineering',
        linkedin_url: 'https://linkedin.com/in/karan-shah-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Priya Jain',
        usn: 'CE2023008',
        batch_year: 2027,
        course_name: 'Civil Engineering',
        linkedin_url: 'https://linkedin.com/in/priya-jain-iitb',
      },
      // First Year Students
      {
        email: '<EMAIL>',
        full_name: 'Dev Sharma',
        usn: 'CS2024009',
        batch_year: 2028,
        course_name: 'Computer Science and Engineering',
        linkedin_url: 'https://linkedin.com/in/dev-sharma-iitb',
      },
      {
        email: '<EMAIL>',
        full_name: 'Sara Khan',
        usn: 'MT2024010',
        batch_year: 2028,
        course_name: 'Metallurgical Engineering and Materials Science',
        linkedin_url: 'https://linkedin.com/in/sara-khan-iitb',
      },
    ],
    nitk: [
      {
        email: '<EMAIL>',
        full_name: 'Rohit Singh',
        usn: 'IT2021001',
        batch_year: 2025,
        course_name: 'Information Technology',
      },
    ],
    iimb: [
      {
        email: '<EMAIL>',
        full_name: 'Ananya Joshi',
        usn: 'MBA2023001',
        batch_year: 2025,
        course_name: 'Master of Business Administration',
      },
    ],
  };

  for (const tenant of createdTenants) {
    const studentUsers = sampleStudentsByTenant[tenant.subdomain] || [];

    for (const userData of studentUsers) {
      const password = await bcrypt.hash('StudentPass123!', 12);

      // Find the course for this user
      const course = await prisma.course.findFirst({
        where: {
          tenant_id: tenant.id,
          course_name: userData.course_name,
        },
      });

      const user = await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: tenant.id,
            email: userData.email,
          },
        },
        update: {},
        create: {
          tenant_id: tenant.id,
          email: userData.email,
          password_hash: password,
          full_name: userData.full_name,
          usn: userData.usn,
          role: UserRole.STUDENT,
          account_status: UserStatus.APPROVED,
        },
      });

      // Create user profile
      await prisma.userProfile.upsert({
        where: {
          user_id: user.id,
        },
        update: {},
        create: {
          user_id: user.id,
          tenant_id: tenant.id,
          course_id: course?.id || null,
          batch_year: userData.batch_year,
          linkedin_url: userData.linkedin_url || null,
          privacy_settings: {
            show_email: false,
            show_mobile: false,
            show_linkedin: true,
            show_location: true,
            show_company: false,
            show_job_title: false,
          },
        },
      });

      console.log(`👨‍🎓 Created student: ${user.email} (${tenant.name})`);
    }
  }

  // Create additional test users for tenant ID 1 (IIT Bombay) for frontend development
  console.log('🧪 Creating additional test users for frontend development...');

  const iitbTenant = createdTenants.find(t => t.subdomain === 'iitb');
  if (iitbTenant) {
    // Test users with different account statuses
    const testUsers = [
      {
        email: '<EMAIL>',
        full_name: 'Pending User',
        usn: 'TEST2024001',
        role: UserRole.ALUMNUS,
        status: UserStatus.PENDING,
        batch_year: 2020,
        course_name: 'Computer Science and Engineering',
      },
      {
        email: '<EMAIL>',
        full_name: 'Rejected User',
        usn: 'TEST2024002',
        role: UserRole.STUDENT,
        status: UserStatus.REJECTED,
        batch_year: 2025,
        course_name: 'Electrical Engineering',
      },
      {
        email: '<EMAIL>',
        full_name: 'Dr. Faculty Member',
        usn: '**********',
        role: UserRole.ALUMNUS,
        status: UserStatus.APPROVED,
        batch_year: 2010, // Faculty as alumni
        course_name: 'Computer Science and Engineering',
        company: 'IIT Bombay',
        job_title: 'Associate Professor',
        location: 'Mumbai, India',
      },
      {
        email: '<EMAIL>',
        full_name: 'Test Admin User',
        usn: 'ADMIN2024001',
        role: UserRole.TENANT_ADMIN,
        status: UserStatus.APPROVED,
        batch_year: null,
        course_name: null,
        company: 'IIT Bombay',
        job_title: 'System Administrator',
        location: 'Mumbai, India',
      },
    ];

    for (const userData of testUsers) {
      const password = await bcrypt.hash('TestPass123!', 12);

      // Find the course for this user (if applicable)
      const course = userData.course_name
        ? await prisma.course.findFirst({
            where: {
              tenant_id: iitbTenant.id,
              course_name: userData.course_name,
            },
          })
        : null;

      const user = await prisma.user.upsert({
        where: {
          idx_tenant_email: {
            tenant_id: iitbTenant.id,
            email: userData.email,
          },
        },
        update: {},
        create: {
          tenant_id: iitbTenant.id,
          email: userData.email,
          password_hash: password,
          full_name: userData.full_name,
          usn: userData.usn,
          role: userData.role,
          account_status: userData.status,
        },
      });

      // Create user profile (only for approved users)
      if (userData.status === UserStatus.APPROVED) {
        await prisma.userProfile.upsert({
          where: {
            user_id: user.id,
          },
          update: {},
          create: {
            user_id: user.id,
            tenant_id: iitbTenant.id,
            course_id: course?.id || null,
            batch_year: userData.batch_year,
            current_location: userData.location || null,
            company: userData.company || null,
            job_title: userData.job_title || null,
            privacy_settings: {
              show_email:
                userData.email.includes('faculty') ||
                userData.role === UserRole.TENANT_ADMIN,
              show_mobile: false,
              show_linkedin: true,
              show_location: true,
              show_company: true,
              show_job_title: true,
            },
          },
        });
      }

      console.log(
        `🧪 Created test user: ${user.email} (${userData.role}, ${userData.status})`
      );
    }
  }

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch(e => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
