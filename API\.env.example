# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/ionalumni?connection_limit=50&pool_timeout=20&socket_timeout=60"

# Database Pool Configuration
DB_CONNECTION_LIMIT=50
DB_READ_CONNECTION_LIMIT=30
DB_POOL_TIMEOUT=20
DB_SOCKET_TIMEOUT=60
DB_QUERY_TIMEOUT=30000

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
JWT_REFRESH_EXPIRES_IN=30d

# Bcrypt Configuration
BCRYPT_SALT_ROUNDS=12

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=IonAlumni

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads/

# Redis Configuration (for distributed caching and queues)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECTION_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=5000

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_CHECK_PERIOD=600
CACHE_MAX_KEYS=50000 
ENABLE_DISTRIBUTED_CACHE=true

# Legacy Node-Cache Configuration (fallback)
NODE_CACHE_DEFAULT_TTL=3600
NODE_CACHE_CHECK_PERIOD=600
NODE_CACHE_MAX_KEYS=10000

# Session Cache Configuration
SESSION_TTL=1800
SESSION_MAX_KEYS=5000

# Rate Limiting Cache Configuration
RATE_LIMIT_TTL=900
RATE_LIMIT_MAX_KEYS=10000

# Rate Limiting (Enhanced for 10K users)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=500
RATE_LIMIT_AUTH_MAX_REQUESTS=10
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
RATE_LIMIT_SKIP_FAILED_REQUESTS=false
ENABLE_DISTRIBUTED_RATE_LIMITING=true

# Queue Configuration (Redis-based for scalability)
ENABLE_REDIS_QUEUES=true
QUEUE_KEEP_COMPLETED=100
QUEUE_KEEP_FAILED=200
QUEUE_MAX_ATTEMPTS=5
QUEUE_BACKOFF_DELAY=2000
QUEUE_CONCURRENCY=10
QUEUE_REMOVE_ON_COMPLETE=100
QUEUE_REMOVE_ON_FAIL=200

# Performance Configuration (Enhanced for 10K users)
ENABLE_CACHE_PRELOADING=true
ENABLE_FAST_SERIALIZATION=true
TARGET_RESPONSE_TIME=50
ENABLE_RESPONSE_COMPRESSION=true
ENABLE_HTTP2=false
ENABLE_KEEP_ALIVE=true
KEEP_ALIVE_TIMEOUT=65000
HEADERS_TIMEOUT=66000

# Clustering Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=0
CLUSTER_RESTART_DELAY=1000
CLUSTER_MAX_RESTARTS=5
CLUSTER_SHUTDOWN_TIMEOUT=10000

# WebSocket Configuration (Enhanced)
WS_PORT=3001
WS_MAX_CONNECTIONS=10000
WS_HEARTBEAT_INTERVAL=30000
WS_CONNECTION_TIMEOUT=60000
ENABLE_WS_CLUSTERING=true



# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Monitoring Configuration
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_CACHE_MONITORING=true
ENABLE_QUEUE_MONITORING=true
