# 🎓 IonAlumni - Alumni Portal Platform

> *A comprehensive multi-tenant alumni portal platform built with modern technologies, featuring real-time communication, job postings, event management, and social networking capabilities for educational institutions.*

---

## ✨ Key Features

### 🏢 **Multi-tenant Architecture**
- Support for multiple educational institutions with complete tenant isolation
- Secure data segregation and customizable branding per institution

### 👥 **User Management & Security**
- **Role-based Access Control**: Student, Alumni, Tenant Admin, Super Admin
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **Email Verification**: Account activation and password reset functionality
- **Token Blacklisting**: Secure logout with token invalidation

### 💬 **Real-time Communication**
- **Socket.IO Integration**: Live messaging and notifications
- **Clustering Support**: Scalable real-time features across multiple instances
- **Event Broadcasting**: Real-time updates for posts, connections, and activities

### 💼 **Job Portal & Events**
- **Job Postings**: Advanced filtering by type, work mode, and experience level
- **Event Management**: Create and manage alumni events with RSVP functionality
- **Career Networking**: Connect alumni with current students and employers

### 🌐 **Social Features**
- **Posts & Feed**: Share updates and achievements
- **Follow System**: Connect with alumni and students with status management
- **Networking**: Build professional relationships within the alumni community

### 📁 **File Management**
- **Profile Pictures**: Upload and manage user avatars
- **Document Storage**: Local file system with secure upload handling
- **Multi-format Support**: Images, documents, and media files

### 🛡️ **Security & Performance**
- **Advanced Security**: CORS, Helmet security headers, input validation
- **In-Memory Caching**: NodeCache for high-performance data caching
- **Rate Limiting**: Configurable request throttling and abuse prevention
- **Response Optimization**: Compression and performance middleware

### 📊 **Admin Dashboard**
- **User Management**: Comprehensive admin panel with bulk operations
- **Analytics & Reports**: User statistics and engagement metrics
- **Content Moderation**: Monitor and manage user-generated content

### 📚 **Developer Experience**
- **Interactive API Documentation**: Swagger/OpenAPI 3.0 with live testing
- **Code Quality**: ESLint, Prettier, TypeScript strict mode
- **Comprehensive Validation**: Input sanitization and error handling

---

## 🏗️ System Architecture

### 🔧 **Backend Technology Stack**

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Runtime** | Node.js + Express.js + TypeScript | High-performance server with type safety |
| **Database** | MySQL + Prisma ORM | Relational data with schema management |
| **Authentication** | JWT + Refresh Tokens | Secure stateless authentication |
| **Real-time** | Socket.IO | Live messaging and notifications |
| **Caching** | NodeCache | In-memory high-performance caching |
| **File Storage** | Multer + Local FS | Profile pictures and document management |
| **Email Service** | Nodemailer + SMTP | Account verification and notifications |
| **Documentation** | Swagger/OpenAPI 3.0 | Interactive API documentation |
| **Security** | Helmet + CORS + Validation | Comprehensive security layers |
| **Code Quality** | ESLint + Prettier + TypeScript | Automated code quality and formatting |

### 🚀 **Performance Features**

- **⚡ In-Memory Caching**: NodeCache for ultra-fast data retrieval
- **🔄 Response Compression**: Gzip compression for optimized bandwidth
- **🌐 Clustering Support**: Multi-core processing for scalability
- **📊 Connection Pooling**: Optimized database connections
- **⏱️ Rate Limiting**: Intelligent request throttling
- **🎯 Query Optimization**: Efficient database queries with Prisma

### Project Structure

```text
IonAlumni/
├── API/                    # Backend API server
│   ├── src/               # Source code
│   │   ├── config/        # Configuration files (database, cache, socket, swagger)
│   │   ├── controllers/   # Route controllers (auth, user, admin, profile, etc.)
│   │   ├── middleware/    # Express middleware (auth, validation, rate limiting)
│   │   ├── routes/        # API routes (auth, user, admin, profile, connections)
│   │   ├── services/      # Business logic services (file, notification, socket)
│   │   ├── handlers/      # Socket.IO event handlers
│   │   ├── types/         # TypeScript type definitions
│   │   ├── utils/         # Utility functions (auth, socket utilities)
│   │   └── viewmodels/    # Data transformation models
│   ├── prisma/            # Database schema, migrations, and seed data
│   ├── uploads/           # File upload directory (profiles, posts, temp)
│   ├── dist/              # Compiled JavaScript (generated)
│   ├── .eslintrc.js       # ESLint configuration
│   ├── .prettierrc.js     # Prettier configuration
│   └── tsconfig.json      # TypeScript configuration
├── database/              # Legacy database migration scripts (db-migrate)
├── frontend/              # Next.js frontend (planned)
├── mobile/                # React Native app (planned)
└── README.md
```

---

## 📋 Prerequisites

> **Before you begin**, ensure your development environment meets these requirements:

### 🛠️ **Required Software**

| Software | Version | Download Link | Purpose |
|----------|---------|---------------|---------|
| **Node.js** | v18.0.0+ | [Download](https://nodejs.org/) | JavaScript runtime |
| **npm/yarn** | npm v8.0.0+ or yarn v1.22.0+ | Included with Node.js | Package manager |
| **MySQL** | v8.0+ | [Download](https://dev.mysql.com/downloads/) | Primary database |
| **Git** | Latest | [Download](https://git-scm.com/downloads) | Version control |

### 💻 **System Requirements**

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **Operating System** | Windows 10, macOS 10.15, Ubuntu 18.04 | Windows 11, macOS 12+, Ubuntu 20.04+ |
| **RAM** | 4GB | 8GB+ |
| **Storage** | 2GB free space | 5GB+ free space |
| **CPU** | Dual-core | Quad-core+ |

### ✅ **Quick Verification**

```bash
# Verify installations
node --version    # Should show v18.0.0 or higher
npm --version     # Should show v8.0.0 or higher
mysql --version   # Should show v8.0 or higher
git --version     # Should show latest version
```

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone https://github.com/sushmitkumarpatil/IonAlumni.git
cd IonAlumni
```

### 2. Backend API Setup

#### Navigate to API Directory
```bash
cd API
```

#### Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

#### Environment Configuration
1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:

   ```env
   # Database Configuration
   DATABASE_URL="mysql://username:password@localhost:3306/ionalumni?connection_limit=50&pool_timeout=20&socket_timeout=60"

   # Database Pool Configuration
   DB_CONNECTION_LIMIT=50
   DB_READ_CONNECTION_LIMIT=30
   DB_POOL_TIMEOUT=20
   DB_SOCKET_TIMEOUT=60
   DB_QUERY_TIMEOUT=30000

   # Server Configuration
   NODE_ENV=development
   PORT=5000
   FRONTEND_URL=http://localhost:3000

   # JWT Configuration (Generate secure keys)
   JWT_SECRET=your-super-secret-jwt-key-here-min-32-chars
   JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-min-32-chars
   JWT_EXPIRES_IN=7d
   JWT_REFRESH_EXPIRES_IN=30d

   # Bcrypt Configuration
   BCRYPT_SALT_ROUNDS=12

   # Email Configuration (for email verification and password reset)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   FROM_EMAIL=<EMAIL>
   FROM_NAME=IonAlumni

   # NodeCache Configuration (In-memory caching)
   CACHE_DEFAULT_TTL=3600
   CACHE_CHECK_PERIOD=600
   CACHE_MAX_KEYS=50000
   CACHE_USE_CLONES=false
   CACHE_DELETE_ON_EXPIRE=true

   # Rate Limiting Configuration
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=500
   RATE_LIMIT_AUTH_MAX_REQUESTS=10

   # File Upload Configuration
   MAX_FILE_SIZE=10485760
   UPLOAD_PATH=./uploads/

   # Performance Configuration
   ENABLE_CACHE_PRELOADING=true
   ENABLE_RESPONSE_COMPRESSION=true
   ENABLE_KEEP_ALIVE=true

   # Clustering Configuration
   CLUSTER_ENABLED=true
   CLUSTER_WORKERS=0

   # WebSocket Configuration
   WS_PORT=3001
   WS_MAX_CONNECTIONS=10000
   WS_HEARTBEAT_INTERVAL=30000
   ENABLE_WS_CLUSTERING=true

   # CORS Configuration
   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
   ```

### 3. Database Setup

#### Create MySQL Database
```sql
-- Connect to MySQL as root or admin user
CREATE DATABASE ionalumni CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create a dedicated user (recommended)
CREATE USER 'ionalumni_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON ionalumni.* TO 'ionalumni_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Update Database URL
Update your `.env` file with the correct database credentials:
```env
DATABASE_URL="mysql://ionalumni_user:secure_password@localhost:3306/ionalumni"
```

### 4. Prisma Setup

#### Generate Prisma Client
```bash
npm run db:generate
```

#### Run Database Migrations
```bash
# Push the schema to your database (for development)
npm run db:push

# Or run migrations (recommended for production)
npm run db:migrate
```

#### Seed the Database

The seed script will create sample data including tenants, courses, and users with proper relationships:

```bash
npm run db:seed
```

**Default Seed Data Created:**

- **Tenants**:
  - Indian Institute of Technology (subdomain: `iit`)
  - Manipal Institute of Technology (subdomain: `mit`)
  - Vellore Institute of Technology (subdomain: `vit`)

- **Courses**: Multiple courses for each tenant (Computer Science, Electronics, Mechanical, etc.)

- **Admin Users** (password: `AdminPass123!`):
  - `<EMAIL>` (IIT Tenant Admin)
  - `<EMAIL>` (MIT Tenant Admin)
  - `<EMAIL>` (VIT Tenant Admin)

- **Sample Alumni** (password: `AlumniPass123!`):
  - Multiple alumni users across different courses and batch years
  - Complete profiles with LinkedIn URLs, companies, and job titles

- **Sample Students** (password: `StudentPass123!`):
  - Current students across various courses
  - Basic profiles with course and batch year information

**Note**: The seed script uses upsert logic to prevent duplicate entries on multiple runs.

### 5. NodeCache Configuration

> **🚀 High-Performance In-Memory Caching**

NodeCache is automatically configured and requires no additional setup! The application uses in-memory caching for:

- **🔄 API Response Caching**: Frequently accessed data
- **👥 User Session Management**: Active user sessions
- **📊 Database Query Results**: Optimized data retrieval
- **⚡ Rate Limiting**: Request throttling and abuse prevention

**Key Benefits:**
- ✅ **Zero Configuration**: Works out of the box
- ✅ **High Performance**: Sub-millisecond access times
- ✅ **Memory Efficient**: Automatic cleanup and TTL management
- ✅ **Thread Safe**: Concurrent access handling
- ✅ **No External Dependencies**: No additional services required

**Cache Statistics** (available in development mode):
```bash
# View cache statistics at runtime
GET /api/cache/stats
```

---

### 6. 🚀 Start the Development Server

#### Run in Development Mode
```bash
npm run dev
```

The server will start on `http://localhost:5000` with the following endpoints:

- **API Base**: `http://localhost:5000/api`
- **API Documentation**: `http://localhost:5000/api-docs` (Interactive Swagger UI)
- **Health Check**: `http://localhost:5000/health`
- **Socket.IO**: `http://localhost:5000` (WebSocket connections)
- **Prisma Studio**: `http://localhost:7000` (Database GUI - run `npm run db:studio`)

#### Build for Production

```bash
# Build the project (includes linting and type checking)
npm run build

# Start production server
npm start
```

## 📚 Available Scripts

### Development Scripts
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm start           # Start production server
npm run clean       # Clean build directory
```

### Database Scripts

```bash
npm run db:generate     # Generate Prisma client
npm run db:push        # Push schema to database (development)
npm run db:migrate     # Run database migrations
npm run db:migrate:prod # Deploy migrations (production)
npm run db:reset       # Reset database and run migrations
npm run db:seed        # Seed database with sample data
npm run db:studio      # Open Prisma Studio on port 7000 (database GUI)
```

### Code Quality Scripts

```bash
npm run lint           # Run ESLint with zero warnings tolerance
npm run lint:fix       # Fix auto-fixable ESLint issues
npm run lint:check     # Check for linting errors
npm run format         # Format code with Prettier
npm run format:check   # Check if code is properly formatted
npm run type-check     # Run TypeScript type checking
npm run code-quality   # Run all quality checks (lint + format + type-check)
npm run code-quality:fix # Fix all auto-fixable issues
```

## 🔧 Configuration

### Environment Variables

### 🗄️ **Database Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | MySQL connection string with pool settings | - | ✅ |
| `DB_CONNECTION_LIMIT` | Database connection pool limit | `50` | ❌ |
| `DB_POOL_TIMEOUT` | Connection pool timeout (seconds) | `20` | ❌ |

### 🔐 **Authentication Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `JWT_SECRET` | JWT signing secret (min 32 chars) | - | ✅ |
| `JWT_REFRESH_SECRET` | JWT refresh token secret (min 32 chars) | - | ✅ |
| `JWT_EXPIRES_IN` | JWT expiration time | `7d` | ❌ |
| `JWT_REFRESH_EXPIRES_IN` | Refresh token expiration | `30d` | ❌ |
| `BCRYPT_SALT_ROUNDS` | Password hashing rounds | `12` | ❌ |

### ⚡ **Caching Configuration (NodeCache)**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `CACHE_DEFAULT_TTL` | Default cache TTL (seconds) | `3600` | ❌ |
| `CACHE_CHECK_PERIOD` | Cache cleanup interval (seconds) | `600` | ❌ |
| `CACHE_MAX_KEYS` | Maximum cached keys | `50000` | ❌ |
| `CACHE_USE_CLONES` | Use object clones for cache | `false` | ❌ |

### 🌐 **Server Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `development` | ❌ |
| `PORT` | Server port | `5000` | ❌ |
| `FRONTEND_URL` | Frontend application URL | `http://localhost:3000` | ❌ |
| `CLUSTER_ENABLED` | Enable clustering | `true` | ❌ |

### 📧 **Email Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SMTP_HOST` | Email server host | `smtp.gmail.com` | ⚠️ |
| `SMTP_USER` | Email username | - | ⚠️ |
| `SMTP_PASS` | Email password/app password | - | ⚠️ |

### 📁 **File Upload Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MAX_FILE_SIZE` | Maximum upload file size | `10485760` (10MB) | ❌ |
| `UPLOAD_PATH` | File upload directory | `./uploads/` | ❌ |

### 🛡️ **Security Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | `500` | ❌ |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window (milliseconds) | `900000` | ❌ |

### 🔌 **WebSocket Configuration**
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `WS_MAX_CONNECTIONS` | Max WebSocket connections | `10000` | ❌ |
| `WS_HEARTBEAT_INTERVAL` | WebSocket heartbeat interval | `30000` | ❌ |

> **Legend**: ✅ Required, ⚠️ Required for full functionality, ❌ Optional

### Security Configuration

#### Generate Secure JWT Secrets
```bash
# Generate random 32-character strings for JWT secrets
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### Rate Limiting

The API includes advanced distributed rate limiting:

- **Default**: 500 requests per 15 minutes per IP
- **Authentication endpoints**: 10 requests per 15 minutes per IP
- **Distributed**: Uses Redis for multi-instance rate limiting
- **Configurable** via environment variables

#### CORS Configuration

Configure allowed origins in your environment:

```env
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

#### Additional Security Features

- **Helmet**: Security headers for XSS, CSRF, and other attacks
- **Token Blacklisting**: JWT tokens can be invalidated on logout
- **Email Verification**: Required for account activation
- **Password Reset**: Secure token-based password reset flow
- **Input Validation**: Comprehensive validation using express-validator

## 🔌 API Documentation

### Swagger/OpenAPI Documentation
Once the server is running, access the interactive API documentation at:
- **Swagger UI**: `http://localhost:5000/api-docs`

### Key API Endpoints

#### Authentication & Account Management

- `POST /api/auth/register` - User registration with tenant context
- `POST /api/auth/login` - User login with tenant validation
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - User logout with token blacklisting
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token
- `POST /api/auth/verify/{token}` - Email verification
- `POST /api/auth/resend-verification` - Resend verification email

#### User & Profile Management

- `GET /api/users/profile` - Get user profile with privacy filtering
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/upload-avatar` - Upload profile picture
- `GET /api/users/search` - Search users within tenant
- `GET /api/profile/{userId}` - Get public profile
- `PUT /api/profile` - Update profile information

#### Admin Management

- `GET /api/admin/users` - List users with filtering
- `PUT /api/admin/users/{userId}/status` - Update user status
- `PUT /api/admin/users/{userId}/role` - Update user role
- `GET /api/admin/reports` - Generate user reports
- `POST /api/admin/bulk-actions` - Bulk user operations

#### Connections & Social

- `POST /api/connections/follow` - Send follow request
- `PUT /api/connections/{connectionId}` - Accept/reject follow request
- `GET /api/connections/followers` - Get followers list
- `GET /api/connections/following` - Get following list
- `DELETE /api/connections/{connectionId}` - Remove connection

### API Authentication

All protected endpoints require a JWT token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

**Multi-tenant Context**: Most endpoints require `tenant_id` in the request body or are automatically scoped to the user's tenant.

## 🧪 Testing

### Manual Testing
1. **Health Check**: Visit `http://localhost:5000/health`
2. **API Documentation**: Visit `http://localhost:5000/api-docs`
3. **Database**: Use `npm run db:studio` to inspect data

### Test User Accounts

After running the seed script, you can use these test accounts:

**Admin Accounts** (password: `AdminPass123!`):

- `<EMAIL>` (IIT Tenant Admin)
- `<EMAIL>` (MIT Tenant Admin)
- `<EMAIL>` (VIT Tenant Admin)

**Alumni Accounts** (password: `AlumniPass123!`):

- `<EMAIL>` (IIT Alumni)
- `<EMAIL>` (MIT Alumni)
- `<EMAIL>` (VIT Alumni)

**Student Accounts** (password: `StudentPass123!`):

- `<EMAIL>` (IIT Student)
- `<EMAIL>` (MIT Student)
- `<EMAIL>` (VIT Student)

**Testing Notes**:

- All accounts are email verified by default in seed data
- Each user has a complete profile with course and batch year
- Alumni accounts have LinkedIn URLs and company information
- Use the appropriate tenant context when testing multi-tenant features

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check if MySQL is running
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Test database connection
mysql -u ionalumni_user -p -h localhost ionalumni
```

#### Port Already in Use
```bash
# Find process using port 5000
lsof -i :5000  # macOS/Linux
netstat -ano | findstr :5000  # Windows

# Kill the process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

#### Prisma Issues
```bash
# Reset Prisma client
rm -rf node_modules/.prisma
npm run db:generate

# Reset database completely
npm run db:reset
npm run db:seed
```

#### NodeCache Issues
```bash
# NodeCache runs in-memory - no external service needed
# Check cache statistics in development mode
curl http://localhost:5000/api/cache/stats

# Clear cache if needed (restart application)
npm run dev
```

### 🔧 **Environment Issues**

- ✅ Ensure all required environment variables are set
- ✅ Check `.env` file syntax (no spaces around `=`)
- ✅ Verify database credentials and connection string
- ✅ Ensure JWT secrets are at least 32 characters long
- ✅ Check SMTP settings for email functionality
- ✅ Verify NodeCache configuration values are numeric

### File Upload Issues

- Check `UPLOAD_PATH` directory exists and has write permissions
- Verify `MAX_FILE_SIZE` is appropriate for your needs
- Ensure disk space is available
- Check that upload directories (`profiles`, `posts`, `temp`) exist

### ⚡ **Performance Issues**

- 📊 Monitor NodeCache memory usage with `/api/cache/stats`
- 🗄️ Check database connection pool settings
- 🔄 Verify clustering is working with `CLUSTER_ENABLED=true`
- 🔌 Monitor WebSocket connection limits
- 🛡️ Check rate limiting configuration for high traffic
- 💾 Monitor application memory usage for cache optimization

## 📝 Development Guidelines

### Code Style

- **TypeScript**: Strict mode enabled with comprehensive type checking
- **ESLint**: Enforced code quality rules with zero warnings tolerance
- **Prettier**: Consistent code formatting with single quotes
- **Naming**: Use camelCase for variables, PascalCase for classes
- **Imports**: Organize imports and use absolute paths where possible
- **Comments**: Use JSDoc for function documentation

### Git Workflow

1. Create feature branches from `main`
2. Run code quality checks before committing:

   ```bash
   npm run code-quality
   ```

3. Write descriptive commit messages following conventional commits
4. Create pull requests for code review
5. Ensure all tests pass and no linting errors

### Database Changes

1. Create Prisma migrations for schema changes:

   ```bash
   npx prisma migrate dev --name your-migration-name
   ```

2. Update seed data if necessary
3. Test migrations on a copy of production data
4. Use transactions for complex data migrations
5. Always backup before running migrations in production

### API Development

- Follow RESTful conventions for endpoint design
- Use proper HTTP status codes
- Implement comprehensive input validation
- Add Swagger documentation for new endpoints
- Ensure proper error handling and logging
- Test with different tenant contexts for multi-tenant features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Run code quality checks: `npm run code-quality`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 👥 Support

For support and questions:
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/sushmitkumarpatil/IonAlumni/issues)
- **Documentation**: [API Documentation](http://localhost:5000/api-docs)

## 🔄 Version History

- **v1.0.0** - Initial release with core features
  - Multi-tenant architecture with tenant isolation
  - User authentication and authorization with JWT
  - Real-time communication with Socket.IO
  - Job and event management
  - File upload capabilities with local storage

- **v1.1.0** - Enhanced features and security
  - Email verification and password reset
  - Token blacklisting for secure logout
  - Admin dashboard with user management
  - Follow system with status management
  - Enhanced rate limiting and caching

- **v1.2.0** - Performance and code quality improvements
  - ESLint and Prettier integration
  - Clustering support for scalability
  - Response optimization and compression
  - NodeCache integration for high-performance caching
  - Comprehensive API documentation

- **v1.3.0** - Enhanced caching and simplified architecture
  - Migrated from Redis to NodeCache for simplified deployment
  - In-memory caching with zero external dependencies
  - Improved performance with sub-millisecond cache access
  - Enhanced developer experience with automatic cache management

---

## 🎉 Happy Coding! 🚀

> **Ready to build amazing alumni connections?**
>
> Start your development journey with IonAlumni and create meaningful connections in the alumni community! 🎓✨
