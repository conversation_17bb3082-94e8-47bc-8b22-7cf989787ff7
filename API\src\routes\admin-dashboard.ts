import express from 'express';
import { query } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation';
import { authenticate, requireTenantAdmin } from '../middleware/auth';
import * as AdminDashboardController from '../controllers/AdminDashboardController';

const router = express.Router();

/**
 * @swagger
 * /api/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/',
  authenticate,
  requireTenantAdmin,
  AdminDashboardController.getDashboardStats
);

/**
 * @swagger
 * /api/admin/dashboard/user-activity:
 *   get:
 *     summary: Get user activity report
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for the report (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: User activity report retrieved successfully
 *       400:
 *         description: Invalid date format
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/user-activity',
  authenticate,
  requireTenantAdmin,
  [
    query('start_date').optional().isISO8601().toDate(),
    query('end_date').optional().isISO8601().toDate(),
  ],
  handleValidationErrors,
  AdminDashboardController.getUserActivityReport
);

/**
 * @swagger
 * /api/admin/dashboard/content-activity:
 *   get:
 *     summary: Get content activity report
 *     tags: [Admin - Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for the report (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Content activity report retrieved successfully
 *       400:
 *         description: Invalid date format
 *       401:
 *         description: Not authenticated
 *       403:
 *         description: Insufficient permissions
 */
router.get(
  '/content-activity',
  authenticate,
  requireTenantAdmin,
  [
    query('start_date').optional().isISO8601().toDate(),
    query('end_date').optional().isISO8601().toDate(),
  ],
  handleValidationErrors,
  AdminDashboardController.getContentActivityReport
);

export default router;
