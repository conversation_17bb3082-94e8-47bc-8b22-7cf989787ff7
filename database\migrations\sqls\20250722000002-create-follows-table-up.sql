CREATE TABLE follows (
    follower_id INT NOT NULL, -- The user who initiates the follow request.
    following_id INT NOT NULL, -- The user who receives the follow request.
    tenant_id INT NOT NULL,
    
    -- Status of the follow request, crucial for the new workflow
    status ENUM('PENDING', 'ACCEPTED', 'REJECTED', 'BLOCKED') NOT NULL DEFAULT 'PENDING',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- The primary key ensures one user cannot have multiple pending requests to the same person.
    PRIMARY KEY (follower_id, following_id),
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);
