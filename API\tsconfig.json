{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "baseUrl": "./", "typeRoots": ["./node_modules/@types", "./src/types"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "src/**/*.json", "temp-routes-v2/profile.ts", "temp-routes-v2/directory.ts", "temp-routes-v2/connections.ts", "temp-routes-v2/authentication.ts", "temp-routes-v2/admin-users.ts", "temp-routes-v2/admin-dashboard.ts", "temp-routes-v2/account.ts", "temp-controllers-v2/ProfileController.ts", "temp-controllers-v2/DirectoryController.ts", "temp-controllers-v2/AuthenticationController.ts", "temp-controllers-v2/AdminUserController.ts", "temp-controllers-v2/AdminDashboardController.ts", "temp-controllers-v2/AccountController.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}