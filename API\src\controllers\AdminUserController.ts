/// <reference path="../types/express.d.ts" />
import { Request, Response, NextFunction } from 'express';
import { UserRole, UserStatus } from '@prisma/client';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types/express';
import { Logger } from '../services/loggerService';
import { toAdminUserViewModel } from '../viewmodels/adminViewModel';
import {
  createSuccessResponse,
  createPaginatedResponse,
  validatePaginationParams,
  calculateSkip,
} from '../viewmodels/responseViewModel';

/**
 * Get All Users with Filtering and Pagination
 * Admin endpoint to manage users
 */
export const getUsers = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;
    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    const { page, limit } = validatePaginationParams(
      req.query.page as string,
      req.query.limit as string
    );
    const search = req.query.search as string;
    const role = req.query.role as UserRole;
    const status = req.query.status as UserStatus;
    const tenantId = req.query.tenant_id as string;

    const skip = calculateSkip(page, limit);

    // Build where clause based on admin permissions
    const whereClause: any = {};

    // Super admin can see all tenants, tenant admin only their tenant
    if (adminUser.role === 'SUPER_ADMIN') {
      if (tenantId) {
        whereClause.tenant_id = parseInt(tenantId);
      }
    } else {
      whereClause.tenant_id = adminUser.tenant_id;
    }

    // Add filters
    if (search) {
      whereClause.OR = [
        {
          full_name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          usn: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (role) {
      whereClause.role = role;
    }

    if (status) {
      whereClause.account_status = status;
    }

    // Fetch users
    const users = await prisma.user.findMany({
      where: whereClause,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
      skip,
      take: limit,
      orderBy: [{ created_at: 'desc' }, { full_name: 'asc' }],
    });

    const total = await prisma.user.count({
      where: whereClause,
    });

    // Apply view model to filter sensitive information
    const filteredUsers = users.map(user => toAdminUserViewModel(user));
    const response = createPaginatedResponse(filteredUsers, page, limit, total);
    res.json(response);

    // Log admin action
    Logger.info('Admin user list accessed', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      filters: { search, role, status, tenantId },
      resultCount: users.length,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get Single User by ID
 * Admin endpoint to view detailed user information
 */
export const getUserById = async (
  req: AuthenticatedRequest<{ id: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;
    const userId = parseInt(req.params.id);

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    // Build where clause based on admin permissions
    const whereClause: any = { id: userId };

    if (adminUser.role !== 'SUPER_ADMIN') {
      whereClause.tenant_id = adminUser.tenant_id;
    }

    const user = await prisma.user.findFirst({
      where: whereClause,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                id: true,
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Apply view model and include additional admin-specific data
    const userData = {
      ...toAdminUserViewModel(user),
      activity_counts: {
        posts: user._count?.general_posts || 0,
        jobs: user._count?.jobs || 0,
        applications: 0, // Not available in current schema
      },
    };

    const response = createSuccessResponse(userData);
    res.json(response);

    // Log admin action
    Logger.info('Admin viewed user details', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      targetUserId: userId,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update User Status
 * Admin endpoint to approve, reject, or deactivate users
 */
export const updateUserStatus = async (
  req: AuthenticatedRequest<{ id: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;
    const userId = parseInt(req.params.id);
    const { status, reason } = req.body;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    if (!['APPROVED', 'REJECTED', 'DEACTIVATED', 'PENDING'].includes(status)) {
      throw createError('Invalid status', 400);
    }

    // Build where clause based on admin permissions
    const whereClause: any = { id: userId };

    if (adminUser.role !== 'SUPER_ADMIN') {
      whereClause.tenant_id = adminUser.tenant_id;
    }

    const user = await prisma.user.findFirst({
      where: whereClause,
      include: {
        tenant: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Prevent admins from modifying other admins (except super admin)
    if (
      user.role === 'SUPER_ADMIN' ||
      (user.role === 'TENANT_ADMIN' && adminUser.role !== 'SUPER_ADMIN')
    ) {
      throw createError('Insufficient permissions to modify this user', 403);
    }

    // Update user status
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        account_status: status as UserStatus,
        updated_at: new Date(),
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
    });

    // TODO: Send notification email to user about status change
    // await NotificationService.sendStatusChangeNotification(updatedUser, status, reason);

    const response = createSuccessResponse({
      user: toAdminUserViewModel(updatedUser),
      message: `User status updated to ${status}`,
    });
    res.json(response);

    // Log admin action
    Logger.info('Admin updated user status', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      targetUserId: userId,
      targetUserEmail: user.email,
      oldStatus: user.account_status,
      newStatus: status,
      reason: reason || 'No reason provided',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update User Role
 * Admin endpoint to change user roles
 */
export const updateUserRole = async (
  req: AuthenticatedRequest<{ id: string }>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;
    const userId = parseInt(req.params.id);
    const { role } = req.body;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    // Only super admin can change roles
    if (adminUser.role !== 'SUPER_ADMIN') {
      throw createError('Only super admin can change user roles', 403);
    }

    if (!['STUDENT', 'ALUMNUS', 'TENANT_ADMIN'].includes(role)) {
      throw createError('Invalid role', 400);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        tenant: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Prevent changing super admin role
    if (user.role === 'SUPER_ADMIN') {
      throw createError('Cannot change super admin role', 403);
    }

    // Update user role
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        role: role as UserRole,
        updated_at: new Date(),
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
    });

    const response = createSuccessResponse({
      user: toAdminUserViewModel(updatedUser),
      message: `User role updated to ${role}`,
    });
    res.json(response);

    // Log admin action
    Logger.info('Admin updated user role', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      targetUserId: userId,
      targetUserEmail: user.email,
      oldRole: user.role,
      newRole: role,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk Update User Status
 * Admin endpoint to update multiple users at once
 */
export const bulkUpdateUserStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;
    const { user_ids, status, reason } = req.body;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      throw createError('user_ids must be a non-empty array', 400);
    }

    if (!['APPROVED', 'REJECTED', 'DEACTIVATED'].includes(status)) {
      throw createError('Invalid status', 400);
    }

    // Build where clause based on admin permissions
    const whereClause: any = {
      id: { in: user_ids.map((id: string) => parseInt(id)) },
    };

    if (adminUser.role !== 'SUPER_ADMIN') {
      whereClause.tenant_id = adminUser.tenant_id;
    }

    // Get users to be updated
    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        role: true,
        account_status: true,
      },
    });

    if (users.length === 0) {
      throw createError('No users found to update', 404);
    }

    // Filter out admin users (except for super admin)
    const usersToUpdate = users.filter(user => {
      if (user.role === 'SUPER_ADMIN') return false;
      if (user.role === 'TENANT_ADMIN' && adminUser.role !== 'SUPER_ADMIN')
        return false;
      return true;
    });

    if (usersToUpdate.length === 0) {
      throw createError('No eligible users found to update', 400);
    }

    // Perform bulk update
    const result = await prisma.user.updateMany({
      where: {
        id: { in: usersToUpdate.map(user => user.id) },
      },
      data: {
        account_status: status as UserStatus,
        updated_at: new Date(),
      },
    });

    const response = createSuccessResponse({
      updated_count: result.count,
      message: `${result.count} users updated to ${status}`,
    });
    res.json(response);

    // Log admin action
    Logger.info('Admin performed bulk user status update', {
      adminId: adminUser.id,
      adminEmail: adminUser.email,
      updatedUserIds: usersToUpdate.map(user => user.id),
      newStatus: status,
      reason: reason || 'No reason provided',
      updatedCount: result.count,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get Pending Users
 * Admin endpoint to get users pending approval
 */
export const getPendingUsers = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminUser = req.user;

    if (!adminUser) {
      throw createError('User not authenticated', 401);
    }

    const { page, limit } = validatePaginationParams(
      req.query.page as string,
      req.query.limit as string
    );
    const skip = calculateSkip(page, limit);

    // Build where clause based on admin permissions
    const whereClause: any = {
      account_status: 'PENDING',
    };

    if (adminUser.role !== 'SUPER_ADMIN') {
      whereClause.tenant_id = adminUser.tenant_id;
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
      skip,
      take: limit,
      orderBy: {
        created_at: 'asc', // Oldest first for approval queue
      },
    });

    const total = await prisma.user.count({
      where: whereClause,
    });

    const filteredUsers = users.map(user => toAdminUserViewModel(user));
    const response = createPaginatedResponse(filteredUsers, page, limit, total);
    res.json(response);
  } catch (error) {
    next(error);
  }
};
