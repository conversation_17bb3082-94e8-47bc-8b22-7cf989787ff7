import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request } from 'express';
import { config } from '../config/config';
import { createError } from './errorHandler';

// Ensure upload directory exists within API folder
const uploadDir = path.resolve(__dirname, '../../', config.upload.uploadPath);
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Create subdirectories for different types of uploads
const createSubDir = (subPath: string) => {
  const fullPath = path.join(uploadDir, subPath);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
  }
  return fullPath;
};

// Ensure subdirectories exist
createSubDir('profiles');
createSubDir('posts');
createSubDir('temp');

// Configure multer storage
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    let subDir = 'temp'; // default subdirectory

    // Determine subdirectory based on the route or field name
    if (
      req.route?.path?.includes('profile') ||
      file.fieldname === 'profilePicture'
    ) {
      subDir = 'profiles';
    } else if (
      req.route?.path?.includes('post') ||
      file.fieldname === 'postImage'
    ) {
      subDir = 'posts';
    }

    const destinationPath = path.join(uploadDir, subDir);
    cb(null, destinationPath);
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    // Generate unique filename with timestamp and random string
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const fileName = `${file.fieldname}-${uniqueSuffix}${fileExtension}`;
    cb(null, fileName);
  },
});

// File filter function to validate file types
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback,
) => {
  // Allowed image types
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(createError('Only image files (JPEG, PNG, GIF, WebP) are allowed', 400));
  }
};

// Create multer instance with configuration
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize, // 10MB by default
    files: 5, // Maximum 5 files per request
  },
  fileFilter,
});

// Middleware for single image upload
export const uploadSingle = (fieldName: string) => upload.single(fieldName);

// Middleware for multiple image uploads
export const uploadMultiple = (fieldName: string, maxCount: number = 5) =>
  upload.array(fieldName, maxCount);

// Middleware for mixed uploads (multiple fields)
export const uploadFields = (fields: { name: string; maxCount?: number }[]) =>
  upload.fields(fields);

// Profile picture upload middleware
export const uploadProfilePicture = uploadSingle('profilePicture');

// Post image upload middleware
export const uploadPostImage = uploadSingle('postImage');

// Multiple post images upload middleware
export const uploadPostImages = uploadMultiple('postImages', 3);

// Error handling middleware for multer errors
export const handleUploadError = (
  error: any,
  req: Request,
  res: any,
  next: any,
) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          error: 'File too large',
          message: `File size must be less than ${config.upload.maxFileSize / (1024 * 1024)}MB`,
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          error: 'Too many files',
          message: 'Maximum number of files exceeded',
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          error: 'Unexpected file field',
          message: 'Unexpected file field in the request',
        });
      default:
        return res.status(400).json({
          error: 'Upload error',
          message: error.message,
        });
    }
  }
  next(error);
};

export default upload;
