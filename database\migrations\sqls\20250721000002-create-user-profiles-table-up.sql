CREATE TABLE user_profiles (
    user_id INT PRIMARY KEY,
    tenant_id INT NOT NULL,
    course_id INT,
    batch_year INT,
    current_location VARCHAR(255),
    linkedin_url VARCHAR(255),
    company VARCHAR(255), -- Only for alumni
    job_title VARCHAR(255), -- Only for alumni
    privacy_settings JSON, -- To store toggles like { "show_email": false, "show_mobile": false }
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL
);
