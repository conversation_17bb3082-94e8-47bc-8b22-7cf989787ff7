import { CacheService } from '../config/cache';
import jwt from 'jsonwebtoken';

export class TokenBlacklistService {
  private static readonly BLACKLIST_PREFIX = 'blacklist:token:';

  static blacklistToken(token: string): Promise<boolean> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-assignment
      const decoded = jwt.decode(token) as any;

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      if (!decoded?.exp) {
        const defaultTTL = 24 * 60 * 60;
        return Promise.resolve(CacheService.set(
          this.getBlacklistKey(token),
          { blacklistedAt: new Date().toISOString() },
          defaultTTL,
        ));
      }

      const now = Math.floor(Date.now() / 1000);
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      const ttl = Math.max(0, decoded.exp - now);

      if (ttl <= 0) {
        return Promise.resolve(true);
      }

      return Promise.resolve(CacheService.set(
        this.getBlacklistKey(token),
        {
          blacklistedAt: new Date().toISOString(),
          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
          originalExp: decoded.exp,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
          userId: decoded.userId,
        },
        ttl,
      ));
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error blacklisting token:', error);
      return Promise.resolve(false);
    }
  }

  static async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistEntry = CacheService.get(this.getBlacklistKey(token));
      return blacklistEntry !== null;
    } catch (error) {
      console.error('Error checking token blacklist:', error);
      return false;
    }
  }

  static async removeFromBlacklist(token: string): Promise<boolean> {
    try {
      return CacheService.del(this.getBlacklistKey(token));
    } catch (error) {
      console.error('Error removing token from blacklist:', error);
      return false;
    }
  }

  static async blacklistAllUserTokens(userId: string): Promise<boolean> {
    try {
      const userBlacklistKey = `${this.BLACKLIST_PREFIX}user:${userId}`;
      const ttl = 24 * 60 * 60;

      return CacheService.set(
        userBlacklistKey,
        {
          blacklistedAt: new Date().toISOString(),
          reason: 'All user tokens blacklisted',
        },
        ttl,
      );
    } catch (error) {
      console.error('Error blacklisting all user tokens:', error);
      return false;
    }
  }

  static async areAllUserTokensBlacklisted(userId: string): Promise<boolean> {
    try {
      const userBlacklistKey = `${this.BLACKLIST_PREFIX}user:${userId}`;
      const blacklistEntry = CacheService.get(userBlacklistKey);
      return blacklistEntry !== null;
    } catch (error) {
      console.error('Error checking user token blacklist:', error);
      return false;
    }
  }

  /**
   * Get cache statistics for blacklisted tokens
   * @returns Object with blacklist statistics
   */
  static getBlacklistStats(): { totalBlacklisted: number } {
    try {
      const keys = CacheService.getKeys();
      const blacklistedTokens = keys.filter(key =>
        key.startsWith(this.BLACKLIST_PREFIX),
      );

      return {
        totalBlacklisted: blacklistedTokens.length,
      };
    } catch (error) {
      console.error('Error getting blacklist stats:', error);
      return { totalBlacklisted: 0 };
    }
  }

  /**
   * Clear all blacklisted tokens (mainly for testing/development)
   * @returns Promise<boolean> - Success status
   */
  static async clearAllBlacklisted(): Promise<boolean> {
    try {
      const keys = CacheService.getKeys();
      const blacklistedKeys = keys.filter(key =>
        key.startsWith(this.BLACKLIST_PREFIX),
      );

      let success = true;
      for (const key of blacklistedKeys) {
        if (!CacheService.del(key)) {
          success = false;
        }
      }

      return success;
    } catch (error) {
      console.error('Error clearing blacklisted tokens:', error);
      return false;
    }
  }

  private static getBlacklistKey(token: string): string {
    const crypto = require('crypto');
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    return `${this.BLACKLIST_PREFIX}${tokenHash}`;
  }
}
