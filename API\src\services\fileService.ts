import fs from 'fs';
import path from 'path';
import { config } from '../config/config';
import { Logger } from './loggerService';

export class FileService {
  private static uploadDir = path.resolve(
    __dirname,
    '../../',
    config.upload.uploadPath,
  );

  /**
   * Get the full path for an uploaded file
   */
  static getFilePath(relativePath: string): string {
    return path.join(this.uploadDir, relativePath);
  }

  /**
   * Get the URL path for serving the file
   */
  static getFileUrl(relativePath: string): string {
    return `/uploads/${relativePath.replace(/\\/g, '/')}`;
  }

  /**
   * Delete a file from the filesystem
   */
  static deleteFile(relativePath: string): Promise<boolean> {
    try {
      const fullPath = this.getFilePath(relativePath);

      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        Logger.info('File deleted successfully', { path: relativePath });
        return Promise.resolve(true);
      }

      Logger.warn('File not found for deletion', { path: relativePath });
      return Promise.resolve(false);
    } catch (error) {
      Logger.error('Error deleting file', {
        path: relativePath,
        error: error instanceof Error ? error.message : String(error),
      });
      return Promise.resolve(false);
    }
  }

  /**
   * Check if a file exists
   */
  static fileExists(relativePath: string): boolean {
    const fullPath = this.getFilePath(relativePath);
    return fs.existsSync(fullPath);
  }

  /**
   * Get file stats
   */
  static getFileStats(relativePath: string): fs.Stats | null {
    try {
      const fullPath = this.getFilePath(relativePath);
      return fs.statSync(fullPath);
    } catch (error) {
      Logger.error('Error getting file stats', {
        path: relativePath,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Move a file from temp directory to permanent location
   */
  static moveFile(
    tempPath: string,
    permanentPath: string,
  ): Promise<boolean> {
    try {
      const sourcePath = this.getFilePath(tempPath);
      const destPath = this.getFilePath(permanentPath);

      // Ensure destination directory exists
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }

      fs.renameSync(sourcePath, destPath);
      Logger.info('File moved successfully', {
        from: tempPath,
        to: permanentPath,
      });
      return Promise.resolve(true);
    } catch (error) {
      Logger.error('Error moving file', {
        from: tempPath,
        to: permanentPath,
        error: error instanceof Error ? error.message : String(error),
      });
      return Promise.resolve(false);
    }
  }

  /**
   * Clean up old temporary files
   */
  static async cleanupTempFiles(maxAgeHours: number = 24): Promise<void> {
    try {
      const tempDir = path.join(this.uploadDir, 'temp');

      if (!fs.existsSync(tempDir)) {
        return;
      }

      const files = fs.readdirSync(tempDir);
      const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          Logger.info('Cleaned up old temp file', { file });
        }
      }
    } catch (error) {
      Logger.error('Error cleaning up temp files', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get relative path from absolute path
   */
  static getRelativePath(absolutePath: string): string {
    return path.relative(this.uploadDir, absolutePath);
  }

  /**
   * Validate image file
   */
  static validateImageFile(file: Express.Multer.File): {
    isValid: boolean;
    error?: string;
  } {
    // Check file size
    if (file.size > config.upload.maxFileSize) {
      return {
        isValid: false,
        error: `File size exceeds ${config.upload.maxFileSize / (1024 * 1024)}MB limit`,
      };
    }

    // Check file type
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
    ];
    if (!allowedTypes.includes(file.mimetype)) {
      return {
        isValid: false,
        error: 'Only image files (JPEG, PNG, GIF, WebP) are allowed',
      };
    }

    return { isValid: true };
  }

  /**
   * Generate a unique filename
   */
  static generateUniqueFilename(
    originalName: string,
    prefix: string = '',
  ): string {
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1e9);
    const extension = path.extname(originalName).toLowerCase();
    return `${prefix}${timestamp}-${random}${extension}`;
  }

  /**
   * Initialize periodic cleanup of temporary files
   */
  static initializeCleanup(): void {
    // Clean up temp files every 6 hours
    setInterval(
      () => {
        this.cleanupTempFiles(24); // Clean files older than 24 hours
      },
      6 * 60 * 60 * 1000,
    );

    // Initial cleanup on startup
    this.cleanupTempFiles(24);
  }
}
