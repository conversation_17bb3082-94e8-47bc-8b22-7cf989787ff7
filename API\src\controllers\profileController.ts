/// <reference path="../types/express.d.ts" />
import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { FileService } from '../services/fileService';
import { toUserProfileViewModel } from '../viewmodels/userProfileViewModel';

interface UpdateProfileRequest {
  full_name?: string;
  mobile_number?: string;
  current_location?: string;
  linkedin_url?: string;
  company?: string;
  job_title?: string;
  course_id?: number;
  batch_year?: number;
  privacy_settings?: {
    show_email?: boolean;
    show_mobile?: boolean;
    show_linkedin?: boolean;
  };
}

/**
 * Get current user's profile
 */
export const getProfile = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId, 10) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                id: true,
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Use view model to filter sensitive info
    const userProfile = toUserProfileViewModel(user, true);

    res.json({
      user: userProfile,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current user info (moved from authController)
 */
export const getCurrentUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update current user's profile
 */
export const updateProfile = async (
  req: Request<{}, {}, UpdateProfileRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const {
      full_name,
      mobile_number,
      current_location,
      linkedin_url,
      company,
      job_title,
      course_id,
      batch_year,
      privacy_settings,
    } = req.body;

    // Update user basic info
    const userUpdateData: any = {};
    if (full_name !== undefined) {
      userUpdateData.full_name = full_name;
    }
    if (mobile_number !== undefined) {
      userUpdateData.mobile_number = mobile_number;
    }

    let updatedUser;
    if (Object.keys(userUpdateData).length > 0) {
      updatedUser = await prisma.user.update({
        where: { id: parseInt(req.user.userId, 10) },
        data: userUpdateData,
      });
    }

    // Update user profile
    const profileUpdateData: any = {};
    if (current_location !== undefined) {
      profileUpdateData.current_location = current_location;
    }
    if (linkedin_url !== undefined) {
      profileUpdateData.linkedin_url = linkedin_url;
    }
    if (company !== undefined) {
      profileUpdateData.company = company;
    }
    if (job_title !== undefined) {
      profileUpdateData.job_title = job_title;
    }
    if (batch_year !== undefined) {
      profileUpdateData.batch_year = batch_year;
    }

    // Validate course_id if provided
    if (course_id !== undefined) {
      if (course_id === null) {
        profileUpdateData.course_id = null;
      } else {
        // Check if course exists and belongs to the same tenant
        const course = await prisma.course.findFirst({
          where: {
            id: course_id,
            tenant_id: req.user.tenant_id,
          },
        });

        if (!course) {
          throw createError(
            'Invalid course ID or course not found in your organization',
            400
          );
        }

        profileUpdateData.course_id = course_id;
      }
    }
    if (privacy_settings !== undefined) {
      // Merge with existing privacy settings
      const existingProfile = await prisma.userProfile.findUnique({
        where: { user_id: parseInt(req.user.userId) },
        select: { privacy_settings: true },
      });

      profileUpdateData.privacy_settings = {
        ...((existingProfile?.privacy_settings as any) || {}),
        ...privacy_settings,
      };
    }

    if (Object.keys(profileUpdateData).length > 0) {
      await prisma.userProfile.upsert({
        where: { user_id: parseInt(req.user.userId) },
        update: profileUpdateData,
        create: {
          user_id: parseInt(req.user.userId),
          tenant_id: req.user.tenant_id,
          ...profileUpdateData,
        },
      });
    }

    // Fetch updated user with profile
    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        profile: {
          include: {
            course: {
              select: {
                id: true,
                course_name: true,
              },
            },
          },
        },
      },
    });

    res.json({
      message: 'Profile updated successfully',
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload profile picture
 */
export const uploadProfilePicture = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    if (!req.file) {
      throw createError('No file uploaded', 400);
    }

    // Validate the uploaded file
    const validation = FileService.validateImageFile(req.file);
    if (!validation.isValid) {
      // Delete the uploaded file if validation fails
      FileService.deleteFile(FileService.getRelativePath(req.file.path));
      throw createError(validation.error || 'Invalid file', 400);
    }

    // Get the relative path for storing in database
    const relativePath = FileService.getRelativePath(req.file.path);
    const imageUrl = FileService.getFileUrl(relativePath);

    // Get current user profile to delete old profile picture if exists
    const currentProfile = await prisma.userProfile.findUnique({
      where: { user_id: parseInt(req.user.userId) },
      select: { profile_picture_url: true },
    });

    // Update user profile with new profile picture
    const updatedProfile = await prisma.userProfile.upsert({
      where: { user_id: parseInt(req.user.userId) },
      update: {
        profile_picture_url: imageUrl,
        updated_at: new Date(),
      },
      create: {
        user_id: parseInt(req.user.userId),
        tenant_id: req.user.tenant_id,
        profile_picture_url: imageUrl,
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Delete old profile picture if it exists and is different
    if (
      currentProfile?.profile_picture_url &&
      currentProfile.profile_picture_url !== imageUrl
    ) {
      const oldRelativePath = currentProfile.profile_picture_url.replace(
        '/uploads/',
        ''
      );
      FileService.deleteFile(oldRelativePath);
    }

    res.json({
      message: 'Profile picture uploaded successfully',
      profilePicture: {
        url: imageUrl,
        uploadedAt: new Date().toISOString(),
      },
      user: updatedProfile.user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // Clean up uploaded file if there's an error
    if (req.file) {
      FileService.deleteFile(FileService.getRelativePath(req.file.path));
    }
    next(error);
  }
};

export const removeProfilePicture = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    res.status(501).json({
      success: false,
      message: 'Profile picture removal not implemented yet',
    });
  } catch (error) {
    next(error);
  }
};

export const getCourses = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    res.status(501).json({
      success: false,
      message: 'Get courses not implemented yet',
    });
  } catch (error) {
    next(error);
  }
};

export const getPrivacySettings = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    res.status(501).json({
      success: false,
      message: 'Get privacy settings not implemented yet',
    });
  } catch (error) {
    next(error);
  }
};

export const updatePrivacySettings = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    res.status(501).json({
      success: false,
      message: 'Update privacy settings not implemented yet',
    });
  } catch (error) {
    next(error);
  }
};
